// 基础股票筛选页面
const filterConfig = require('../../../config/filterCondition');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 筛选条件配置
    filterConfig: filterConfig,
    
    // 当前选中的筛选条件组
    activeGroup: 'dailyConditions',
    
    // 用户输入的筛选条件
    filterConditions: {
      dailyConditions: {},
      financialConditions: {},
      basicInfoConditions: {}
    },
    
    // 目标日期
    targetDate: '',
    
    // 弹窗相关
    showModal: false,
    currentField: null,
    currentGroup: null,
    currentFieldConfig: null,
    
    // 临时输入值（用于弹窗中的输入）
    tempValue: null,
    
    // 已选指标弹窗
    showSelectedModal: false,
    
    // 已选指标数量
    selectedCount: 0,
    
    // 已选条件列表（用于已选指标弹窗）
    selectedConditionsList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置默认日期为今天
    const today = new Date();
    const dateStr = this.formatDate(today);
    this.setData({
      targetDate: dateStr
    });
    
    // 计算已选指标数量
    this.updateSelectedCount();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时重新计算已选指标数量
    this.updateSelectedCount();
  },

  /**
   * 切换筛选条件组
   */
  switchGroup(e) {
    const groupKey = e.currentTarget.dataset.group;
    this.setData({
      activeGroup: groupKey
    });
  },

  /**
   * 点击筛选字段，打开设置弹窗
   */
  openFieldModal(e) {
    const { group, field } = e.currentTarget.dataset;
    const fieldConfig = this.data.filterConfig[group][field];
    const currentValue = this.data.filterConditions[group][field];
    
    // 准备临时值
    let tempValue;
    if (fieldConfig.type === 'range') {
      tempValue = currentValue || { min: '', max: '' };
    } else if (fieldConfig.type === 'multiSelect') {
      tempValue = currentValue || [];
    } else {
      tempValue = currentValue || false;
    }
    
    this.setData({
      showModal: true,
      currentField: field,
      currentGroup: group,
      currentFieldConfig: fieldConfig,
      tempValue: tempValue
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  /**
   * 关闭设置弹窗
   */
  closeModal() {
    this.setData({
      showModal: false,
      currentField: null,
      currentGroup: null,
      currentFieldConfig: null,
      tempValue: null
    });
  },

  /**
   * 处理范围输入 - 最小值
   */
  onTempRangeMinInput(e) {
    const value = e.detail.value;
    this.setData({
      'tempValue.min': value
    });
  },

  /**
   * 处理范围输入 - 最大值
   */
  onTempRangeMaxInput(e) {
    const value = e.detail.value;
    this.setData({
      'tempValue.max': value
    });
  },

  /**
   * 处理布尔值切换
   */
  onTempBooleanToggle(e) {
    const checked = e.detail.value;
    this.setData({
      tempValue: checked
    });
  },

  /**
   * 处理多选切换
   */
  onTempMultiSelectToggle(e) {
    const { value } = e.currentTarget.dataset;
    const checked = e.detail.value;
    const currentList = this.data.tempValue || [];

    console.log('多选切换 - 字段:', this.data.currentField);
    console.log('多选切换 - 值:', value);
    console.log('多选切换 - 选中状态:', checked);
    console.log('多选切换 - 当前列表:', currentList);

    let newList;
    if (checked) {
      newList = [...currentList, value];
    } else {
      newList = currentList.filter(item => item !== value);
    }

    console.log('多选切换 - 新列表:', newList);

    this.setData({
      tempValue: newList
    });
  },

  /**
   * 确认设置筛选条件
   */
  confirmFieldSetting() {
    const { currentGroup, currentField, tempValue, currentFieldConfig } = this.data;

    console.log('确认设置 - 组:', currentGroup);
    console.log('确认设置 - 字段:', currentField);
    console.log('确认设置 - 临时值:', tempValue);
    console.log('确认设置 - 字段配置:', currentFieldConfig);

    // 验证数据
    if (currentFieldConfig.type === 'range') {
      const { min, max } = tempValue;
      if (min !== '' && max !== '' && parseFloat(min) > parseFloat(max)) {
        wx.showToast({
          title: '最小值不能大于最大值',
          icon: 'none'
        });
        return;
      }
      
      // 如果都为空，则删除该条件
      if (min === '' && max === '') {
        const newConditions = { ...this.data.filterConditions };
        delete newConditions[currentGroup][currentField];
        this.setData({
          filterConditions: newConditions
        });
      } else {
        const updatePath = `filterConditions.${currentGroup}.${currentField}`;
        this.setData({
          [updatePath]: {
            min: min === '' ? null : parseFloat(min),
            max: max === '' ? null : parseFloat(max)
          }
        });
      }
    } else if (currentFieldConfig.type === 'multiSelect') {
      console.log('多选确认 - tempValue长度:', tempValue.length);
      console.log('多选确认 - tempValue内容:', tempValue);

      if (tempValue.length === 0) {
        // 删除该条件
        console.log('多选确认 - 删除条件');
        const newConditions = { ...this.data.filterConditions };
        delete newConditions[currentGroup][currentField];
        this.setData({
          filterConditions: newConditions
        });
      } else {
        console.log('多选确认 - 设置条件');
        const updatePath = `filterConditions.${currentGroup}.${currentField}`;
        this.setData({
          [updatePath]: tempValue
        });
        console.log('多选确认 - 设置后的筛选条件:', this.data.filterConditions);
      }
    } else {
      // 布尔值
      if (tempValue) {
        const updatePath = `filterConditions.${currentGroup}.${currentField}`;
        this.setData({
          [updatePath]: tempValue
        });
      } else {
        // 删除该条件
        const newConditions = { ...this.data.filterConditions };
        delete newConditions[currentGroup][currentField];
        this.setData({
          filterConditions: newConditions
        });
      }
    }
    
    this.closeModal();
    this.updateSelectedCount();
  },

  /**
   * 显示已选指标
   */
  showSelectedConditions() {
    this.setData({
      showSelectedModal: true
    });
  },

  /**
   * 关闭已选指标弹窗
   */
  closeSelectedModal() {
    this.setData({
      showSelectedModal: false
    });
  },

  /**
   * 删除单个已选条件
   */
  removeSelectedCondition(e) {
    const { group, field } = e.currentTarget.dataset;
    const newConditions = { ...this.data.filterConditions };
    delete newConditions[group][field];
    
    this.setData({
      filterConditions: newConditions
    });
    
    this.updateSelectedCount();
  },

  /**
   * 编辑已选条件
   */
  editSelectedCondition(e) {
    const { group, field } = e.currentTarget.dataset;
    
    // 关闭已选指标弹窗
    this.setData({
      showSelectedModal: false
    });
    
    // 切换到对应的标签页
    this.setData({
      activeGroup: group
    });
    
    // 打开编辑弹窗
    const fieldConfig = this.data.filterConfig[group][field];
    const currentValue = this.data.filterConditions[group][field];
    
    // 准备临时值
    let tempValue;
    if (fieldConfig.type === 'range') {
      tempValue = currentValue || { min: '', max: '' };
    } else if (fieldConfig.type === 'multiSelect') {
      tempValue = currentValue || [];
    } else {
      tempValue = currentValue || false;
    }
    
    this.setData({
      showModal: true,
      currentField: field,
      currentGroup: group,
      currentFieldConfig: fieldConfig,
      tempValue: tempValue
    });
  },

  /**
   * 处理日期选择
   */
  onDateChange(e) {
    this.setData({
      targetDate: e.detail.value
    });
  },

  /**
   * 重置筛选条件
   */
  resetConditions() {
    this.setData({
      filterConditions: {
        dailyConditions: {},
        financialConditions: {},
        basicInfoConditions: {}
      },
      selectedCount: 0
    });
    
    wx.showToast({
      title: '已重置条件',
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 更新已选指标数量
   */
  updateSelectedCount() {
    const conditions = this.data.filterConditions;
    let count = 0;
    
    for (let groupKey in conditions) {
      count += Object.keys(conditions[groupKey]).length;
    }
    
    // 更新已选条件列表
    const selectedList = this.getSelectedConditionsList();
    
    this.setData({
      selectedCount: count,
      selectedConditionsList: selectedList
    });
  },

  /**
   * 开始查询（暂时只显示提示）
   */
  startQuery() {
    // 检查是否设置了筛选条件
    const hasConditions = this.checkHasConditions();
    
    if (!hasConditions) {
      wx.showToast({
        title: '请设置筛选条件',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 暂时只显示提示，后续连接API
    wx.showToast({
      title: '查询功能待开发',
      icon: 'none',
      duration: 2000
    });
    
    console.log('筛选条件:', this.data.filterConditions);
    console.log('目标日期:', this.data.targetDate);
  },

  /**
   * 检查是否有筛选条件
   */
  checkHasConditions() {
    const conditions = this.data.filterConditions;
    
    for (let groupKey in conditions) {
      const group = conditions[groupKey];
      if (Object.keys(group).length > 0) {
        return true;
      }
    }
    
    return false;
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 检查字段是否已设置
   */
  isFieldSet(group, field) {
    return this.data.filterConditions[group] && this.data.filterConditions[group][field] !== undefined;
  },

  /**
   * 获取字段显示值
   */
  getFieldDisplayValue(group, field) {
    const value = this.data.filterConditions[group][field];
    const config = this.data.filterConfig[group][field];
    
    if (!value) return '';
    
    if (config.type === 'range') {
      const { min, max } = value;
      if (min !== null && max !== null) {
        return `${min} - ${max}`;
      } else if (min !== null) {
        return `≥ ${min}`;
      } else if (max !== null) {
        return `≤ ${max}`;
      }
    } else if (config.type === 'multiSelect') {
      return `已选${value.length}项`;
    } else if (config.type === 'boolean') {
      return value ? '是' : '否';
    }
    
    return '';
  },

  /**
   * 获取已选条件列表（用于已选指标弹窗）
   */
  getSelectedConditionsList() {
    const conditions = this.data.filterConditions;
    const config = this.data.filterConfig;
    const result = [];
    
    for (let groupKey in conditions) {
      const group = conditions[groupKey];
      const groupConfig = this.data.filterConfig.conditionGroups.find(g => g.key === groupKey);
      
      for (let fieldKey in group) {
        const fieldConfig = config[groupKey][fieldKey];
        const value = group[fieldKey];
        
        result.push({
          groupKey,
          groupLabel: groupConfig.label,
          fieldKey,
          fieldLabel: fieldConfig.label,
          value,
          displayValue: this.getFieldDisplayValue(groupKey, fieldKey)
        });
      }
    }
    
    return result;
  },
});
