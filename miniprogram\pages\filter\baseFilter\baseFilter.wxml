<!--基础股票筛选页面-->
<view class="filter-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">股票筛选</text>
    <text class="page-subtitle">设置筛选条件，精准选股</text>
  </view>

  <!-- 筛选条件分组选项卡 -->
  <view class="filter-tabs">
    <block wx:for="{{filterConfig.conditionGroups}}" wx:key="key">
      <view 
        class="tab-item {{activeGroup === item.key ? 'active' : ''}}"
        bindtap="switchGroup"
        data-group="{{item.key}}"
      >
        <text class="tab-label">{{item.label}}</text>
      </view>
    </block>
  </view>

  <!-- 筛选条件网格 -->
  <scroll-view class="filter-content" scroll-y="{{true}}">
    
    <!-- 当日数据筛选条件 -->
    <view class="condition-grid" wx:if="{{activeGroup === 'dailyConditions'}}">
      <block wx:for="{{filterConfig.dailyConditions}}" wx:key="index" wx:for-item="config" wx:for-index="fieldKey">
        <view 
          class="condition-card {{isFieldSet('dailyConditions', fieldKey) ? 'selected' : ''}}"
          bindtap="openFieldModal"
          data-group="dailyConditions"
          data-field="{{fieldKey}}"
        >
          <text class="card-label">{{config.label}}</text>
          <view class="card-status" wx:if="{{isFieldSet('dailyConditions', fieldKey)}}">
            <text class="status-text">{{getFieldDisplayValue('dailyConditions', fieldKey)}}</text>
          </view>
        </view>
      </block>
    </view>

    <!-- 财务数据筛选条件 -->
    <view class="condition-grid" wx:if="{{activeGroup === 'financialConditions'}}">
      <block wx:for="{{filterConfig.financialConditions}}" wx:key="index" wx:for-item="config" wx:for-index="fieldKey">
        <view 
          class="condition-card {{isFieldSet('financialConditions', fieldKey) ? 'selected' : ''}}"
          bindtap="openFieldModal"
          data-group="financialConditions"
          data-field="{{fieldKey}}"
        >
          <text class="card-label">{{config.label}}</text>
          <view class="card-status" wx:if="{{isFieldSet('financialConditions', fieldKey)}}">
            <text class="status-text">{{getFieldDisplayValue('financialConditions', fieldKey)}}</text>
          </view>
        </view>
      </block>
    </view>

    <!-- 基本信息筛选条件 -->
    <view class="condition-grid" wx:if="{{activeGroup === 'basicInfoConditions'}}">
      <block wx:for="{{filterConfig.basicInfoConditions}}" wx:key="index" wx:for-item="config" wx:for-index="fieldKey">
        <view 
          class="condition-card {{isFieldSet('basicInfoConditions', fieldKey) ? 'selected' : ''}}"
          bindtap="openFieldModal"
          data-group="basicInfoConditions"
          data-field="{{fieldKey}}"
        >
          <text class="card-label">{{config.label}}</text>
          <view class="card-status" wx:if="{{isFieldSet('basicInfoConditions', fieldKey)}}">
            <text class="status-text">{{getFieldDisplayValue('basicInfoConditions', fieldKey)}}</text>
          </view>
        </view>
      </block>
    </view>

  </scroll-view>

  <!-- 底部操作区 -->
  <view class="filter-actions">
    <!-- 目标日期选择 -->
    <view class="date-picker">
      <text class="date-label">目标日期：</text>
      <picker 
        mode="date" 
        value="{{targetDate}}" 
        bindchange="onDateChange"
        class="date-picker-input"
      >
        <text class="date-value">{{targetDate || '请选择日期'}}</text>
      </picker>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="btn-selected" bindtap="showSelectedConditions">
        已选指标 {{selectedCount}}
      </button>
      <button class="btn-reset" bindtap="resetConditions">重置条件</button>
      <button class="btn-query" bindtap="startQuery">查看结果</button>
    </view>
  </view>

  <!-- 字段设置弹窗 -->
  <view class="modal-overlay {{showModal ? 'show' : ''}}" bindtap="closeModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{currentFieldConfig.label}}</text>
        <view class="modal-close" bindtap="closeModal">✕</view>
      </view>
      
      <view class="modal-body">
        <text class="field-desc">{{currentFieldConfig.description}}</text>
        
        <!-- 范围输入 -->
        <view class="range-setting" wx:if="{{currentFieldConfig.type === 'range'}}">
          <view class="range-item">
            <text class="range-label">最小值</text>
            <input 
              class="range-input-modal"
              type="digit"
              placeholder="请输入最小值"
              value="{{tempValue.min}}"
              bindinput="onTempRangeMinInput"
              bindtap="stopPropagation"
              bindfocus="stopPropagation"
            />
            <text class="range-unit" wx:if="{{currentFieldConfig.unit}}">{{currentFieldConfig.unit}}</text>
          </view>
          <view class="range-item">
            <text class="range-label">最大值</text>
            <input 
              class="range-input-modal"
              type="digit"
              placeholder="请输入最大值"
              value="{{tempValue.max}}"
              bindinput="onTempRangeMaxInput"
              bindtap="stopPropagation"
              bindfocus="stopPropagation"
            />
            <text class="range-unit" wx:if="{{currentFieldConfig.unit}}">{{currentFieldConfig.unit}}</text>
          </view>
        </view>

        <!-- 布尔值设置 -->
        <view class="boolean-setting" wx:if="{{currentFieldConfig.type === 'boolean'}}">
          <label class="boolean-option">
            <switch 
              checked="{{tempValue}}"
              bindchange="onTempBooleanToggle"
              bindtap="stopPropagation"
            />
            <text class="boolean-label">启用此条件</text>
          </label>
        </view>

        <!-- 多选设置 -->
        <view class="multiselect-setting" wx:if="{{currentFieldConfig.type === 'multiSelect'}}">
          <text class="setting-subtitle">{{currentFieldConfig.description}}，支持多选。</text>
          <view class="option-list">
            <!-- 统一使用对象数组格式 -->
            <block wx:for="{{currentFieldConfig.options}}" wx:key="value">
              <label class="option-item">
                <checkbox
                  value="{{item.value}}"
                  checked="{{tempValue.includes(item.value)}}"
                  data-value="{{item.value}}"
                  bindchange="onTempMultiSelectToggle"
                  bindtap="stopPropagation"
                />
                <text class="option-label">{{item.label}}</text>
              </label>
            </block>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-cancel" bindtap="closeModal">取消</button>
        <button class="btn-confirm" bindtap="confirmFieldSetting">确定</button>
      </view>
    </view>
  </view>

  <!-- 已选指标弹窗 -->
  <view class="modal-overlay {{showSelectedModal ? 'show' : ''}}" bindtap="closeSelectedModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">已选指标 {{selectedCount}}</text>
        <view class="modal-close" bindtap="closeSelectedModal">✕</view>
      </view>
      
      <view class="modal-body">
        <view class="selected-list" wx:if="{{selectedCount > 0}}">
          <block wx:for="{{selectedConditionsList}}" wx:key="index">
            <view class="selected-item">
              <view class="selected-info">
                <text class="selected-group">{{item.groupLabel}}</text>
                <text class="selected-field">{{item.fieldLabel}}</text>
                <text class="selected-value">{{item.displayValue}}</text>
              </view>
              <view class="selected-actions">
                <view 
                  class="selected-edit"
                  bindtap="editSelectedCondition"
                  data-group="{{item.groupKey}}"
                  data-field="{{item.fieldKey}}"
                >
                  编辑
                </view>
                <view 
                  class="selected-remove"
                  bindtap="removeSelectedCondition"
                  data-group="{{item.groupKey}}"
                  data-field="{{item.fieldKey}}"
                >
                  删除
                </view>
              </view>
            </view>
          </block>
        </view>
        <view class="empty-selected" wx:else>
          <text>暂无已选指标</text>
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-confirm" bindtap="closeSelectedModal">确定</button>
      </view>
    </view>
  </view>

</view>
